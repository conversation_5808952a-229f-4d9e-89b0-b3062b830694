# Instructions

Read the feature overview document located at `docs/reports/FEATURE_OVERVIEW.md` think about it step by step.

Implement all the features listed in the document using vanilla JavaScript, HTML, and CSS. Do not add any frameworks or libraries. The only libraries used for this project are:

- [<PERSON>ca](js/lib/pica/pica.min.js) - image resizing
- [PhotoSwipe](js/lib/photoswipe/photoswipe.umd.min.js) - lightbox viewer
- [JSZip](js/lib/jszip/jszip.min.js) - ZIP generation

The existing `index.html` and `css/styles.css` files contains the layout and structure for the application. Build upon this foundation to implement the required features.

Place the core application logic in the `js/app.js` file. This is where you will implement all of application functionality.

Update `index.html` and `css/styles.css` as needed to support the new features.

## Libraries

Use `get-library-docs` tool to look up documentation for the following libraries. The library IDs are:

- `Pica`: /nodeca/pica
- `PhotoSwipe`: /dimsemenov/photoswipe
- `JSZip`: /stuk/jszip

### Pica

The Pica object is available globally as `pica`. It does not need to be created.

Wrong:

```javascript
const pica = new Pica();
pica.resize(fromCanvas, toCanvas, options);
```

Correct:

```javascript
pica.resize(fromCanvas, toCanvas, options);
```

## Coding Standards

- Simplicity and elegance code is paramount.
- When creating new functionality, scan the project existing files and think about all of the existing functionality that could be updated to support the same use case. Avoid creating new functionality unless absolutely necessary.
- If you find yourself writing a lot of code, stop and think if there's a simpler way to achieve the same goal.
- Less code is better than more — aim for the smallest, clearest solution that works.
- Keep the code simple and maintainable.
- Do not add comments unless absolutely necessary, the code should be self-explanatory.
- Never use null, always use undefined for optional values.
- Use simple, clear and minimal code. Favour brevity over descriptiveness.

## JavaScript

- Don't use ESM modules at all.
- Use vanilla JavaScript only. No frameworks or libraries except the ones explicitly allowed below.
- Explicitly declare all variables. Use `let` and `const` instead of `var`.
- Use simple, clear and minimal variable and function names. Favour brevity over descriptiveness.
- Prefer single-letter names for short-lived variables in small scopes (e.g. loop indices).
- Create utility functions for repeated code patterns. THIS IS IMPARATIVE
- Create a function called `$` for accessing DOM elements by ID. THIS IS IMPARATIVE
- Create a function called `$$` for accessing DOM elements by CSS selector. THIS IS IMPARATIVE
- Don't create HTML elements using JavaScript DOM methods. THIS IS IMPARATIVE. Use HTML template literals for generating HTML content.

## HTML

- Use HTML template literals for generating HTML content.
- Don't create HTML elements using JavaScript DOM methods.
- Create HTML templates in the HTML file and clone them in JavaScript.

### Logical OR vs Nullish Coalescing (|| vs ??)

BEFORE using || or ?? operators, ALWAYS ask these questions:

1. **Is 0 a valid value for this property?** If YES, use ??
2. **Is false a valid value for this property?** If YES, use ??
3. **Is empty string ("") a valid value for this property?** If YES, use ??
4. **Do I want ALL falsy values (0, false, "", null, undefined, NaN) to trigger the default?** If YES, use ||
5. **Do I want ONLY null/undefined to trigger the default?** If YES, use ??

EXAMPLES:

- `width = value || 400` ✓ (0 width is invalid)
- `horizontalOffset = value ?? 50` ✓ (0 offset is valid - left edge)
- `transparentBackground = value ?? false` ✓ (false is valid)
- `name = value || "Default"` ✓ (empty string should default)
- `quality = value ?? 0` ✓ (0 quality is valid)

NEVER assume || is correct. ALWAYS verify the logic first.
